# Default values for chart.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  appname: APPNAME
  env: ENV
  
persistentVolumeClaim:
  enabled: false
  pvc:
    - name: APPNAME
      accessMode: ReadWriteOnce
      storage: 5Gi
      storageClassName: default
#    - name: another-pvc
#      accessMode: ReadWriteMany
#      storage: 5Gi
#      storageClassName: fast-storage

service:
  enabled: false
  type: ClusterIP
  # nodePort: 31188
  port: 80
  targetPort: PORT
  # selector:
  #   app: APPNAME
  # extraPort:
  #   - name: metrics
  #     protocol: TCP
  #     port: 88
  #     targetPort: 8082

ingress:
  enabled: false
  className: "nginx"
  annotations:
    # kubernetes/ingress-nginx: https://kubernetes.github.io/ingress-nginx/user-guide/nginx-configuration/annotations/
    # nginxinc/kubernetes-ingress: https://docs.nginx.com/nginx-ingress-controller/configuration/ingress-resources/advanced-configuration-with-annotations/
  hosts:
    - host: HOSTNAME
      http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: APPNAME
              port:
                number: 80
  # tls:
  #   - secretName: SECRET-TLS
  #     hosts:
  #       - HOSTNAME

deployment:
  - name: APPNAME
    replicaCount: REPLICAS
    image:
      repository: harbor.dbiz.vn/tier/APPNAME
      pullPolicy: Always
      # Overrides the image tag whose default is the chart appVersion.
      tag: "IMAGE_VERSION"
    labels:
      app: APPNAME
      env: ENV
      tier: tier
      devops: devops
      owner: owner
    strategy:
      type: RollingUpdate
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 25%
    imagePullSecrets:
      - name: dockerhub-private
    containerName: "APPNAME"
    nameOverride: ""
    fullnameOverride: ""
    ## Overrides pod.spec.hostAliases in the deployment's pods
    # hostAliases:
    #   - ip: "*************"
    #     hostnames:
    #     - "host.domain.com"
    ## Extra environment variables that will be pass onto deployment pods
    # env:
    # - name: ECHO_VAR
    #   value: "busybox"
    ## Using Secrets as environment variables
    envFrom:
    - secretRef:
        name: APPNAME
    externalsecret:
      name: APPNAME
      dataFrom: tier/ENV/APPNAME
    ## Using Secrets as files from a Pod, that mounts a Secret in a volume
    # volumes:
    #   - name: APPNAME-volume
    #     secret:
    #       secretName: APPNAME
    #   - name: tmp-volume
    #     emptyDir: {}
    # volumeMounts:
    #   - name: APPNAME-volume
    #     mountPath: "/app/env"
    #   - name: tmp-volume
    #     mountPath: "/tmp"
    ## Using command overwrite entrypoint.sh
    # command: ["/bin/sh"]
    # args:
    #   - '-c'
    #   - 'for i in $(ls /app/env/); do echo "$i=$(cat /app/env/$i)" >> /app/.env; done; /app/scripts/run/JOB_NAME.sh'
    podAnnotations:
      # Auto rollout restart deployment when secret changed
      secret.reloader.stakater.com/reload: APPNAME
      container.apparmor.security.beta.kubernetes.io/APPNAME: runtime/default
    podSecurityContext: {}
      # fsGroup: 1000
      # seccompProfile:
      #   type: RuntimeDefault
    securityContext: {}
      # privileged: false
      # allowPrivilegeEscalation: false
      # readOnlyRootFilesystem: true
      # runAsNonRoot: true
      # runAsUser: 1000
      # runAsGroup: 2000
      # capabilities:
      #   drop:
      #     - ALL
    # livenessProbe: 
    #   httpGet:
    #     httpHeaders:
    #     - name: Host
    #       value: HOSTNAME
    #     path: /
    #     port: PORT
    #   initialDelaySeconds: 30
    #   periodSeconds: 10
    #   timeoutSeconds: 5
    #   failureThreshold: 3
    #   successThreshold: 1
    # readinessProbe: 
    #   httpGet:
    #     httpHeaders:
    #     - name: Host
    #       value: HOSTNAME
    #     path: /
    #     port: PORT
    #   initialDelaySeconds: 30
    #   periodSeconds: 10
    #   timeoutSeconds: 5
    #   failureThreshold: 3
    #   successThreshold: 1
    resources:
      limits:
        cpu: 2000m
        memory: 2048Mi
      requests:
        cpu: 50m
        memory: 64Mi
    # lifecycle:
    #   preStop:
    #     exec:
    #       command: ["/bin/sh", "-c", "sleep 15"]
    autoscaling:
      enabled: false
      minReplicas: 1
      maxReplicas: 10
      targetCPUUtilizationPercentage: 80
      # targetMemoryUtilizationPercentage: 80
    # fluentbit:
    #   repository: registry.dbiz.vn/devops/fluentbit
    #   tag: v0.0.5
    #   env: ENV
    #   secretName: fluentbit-v2
    # redis:
    #   image: registry.dbiz.vn/dockerhub/redis:5.0.4
    # logstash:
    #   image: registry.dbiz.vn/devops/logstash:v1.0.0
    # fluentbitBigdata:
    #   repository: registry.dbiz.vn/devops/fluentbit
    #   tag: v1.1.4
    #   env: ENV
    # initContainers:
    #   - name: init-myservice
    #     image: busybox:1.28
    #     command: ['sh', '-c', 'sleep 20']
    # networkpolicy:
    #   ingress:
    #     ## Allow all
    #     - {}
        ## Example ingress rule
        # - from:
        #   - ipBlock:
        #       cidr: **********/16
        #       except:
        #       - **********/24
        #   - namespaceSelector:
        #       matchLabels:
        #         project: myproject
        #   - podSelector:
        #       matchLabels:
        #         role: frontend
        #   ports:
        #   - protocol: TCP
        #     port: 6379
      # egress:
      #   ## Allow all
      #   - {}
        ## Example egress rule
        # - to:
        #   - ipBlock:
        #       cidr: 10.0.0.0/24
        #   ports:
        #   - protocol: TCP
        #     port: 5978
    ## The configuration to be loaded to pods
    # haproxy:
    #   repository: registry.dbiz.vn/devops/haproxy
    #   tag: 2.2.17-alpine
    # secret:
    #   data:
    #     key: value
    # configmap:
    #   data:
    #     haproxy.cfg: |+
    #       global
    #         maxconn 32000
    #         user root
    #         group root
    #         stats timeout 30s
    #         daemon

    #       defaults
    #         log global
    #         mode http
    #         option http-server-close
    #         retries 3
    #         option redispatch
    #         timeout http-request 10s
    #         timeout connect 5s
    #         timeout server 15m
    #         timeout client 15m
    #         timeout http-keep-alive 30s

    #       listen es_backend
    #         bind :9200
    #         mode http
    #         option forwardfor
    #         option httpclose
    #         balance leastconn
    #         server 10.110.128.86  10.110.128.86:9200  check inter 2s rise 3 fall 3
    #         server 10.110.132.109 10.110.132.109:9200  check inter 2s rise 3 fall 3

    #       listen stats
    #         bind 0.0.0.0:8382
    #         mode http
    #         stats enable
    #         stats hide-version
    #         stats realm HAproxy-Statistics
    #         stats uri /ha-stats
    #         stats refresh 10s
    terminationGracePeriodSeconds: 30
    nodeSelector: {}
    tolerations: []
    affinity: {}

# Define for cron job
jobs: []
  # - name: JOBNAME
  #   labels:
  #     env: ENV
  #     app: APPNAME
  #     owner: owner
  #     devops: devops
  #     tier: tier
  #   image:
  #     repository: registry.dbiz.vn/tier/APPNAME
  #     tag: "IMAGE_VERSION"
  #     imagePullPolicy: Always
  #   imagePullSecrets:
  #     - name: dockerhub-private
  #   hostAliases:
  #     - ip: ********
  #       hostnames:
  #       - "host.domain.com"
  #   # optional env vars
  #   env:
  #   - name: ECHO_VAR
  #     value: "busybox"
  #   envFrom:
  #   - secretRef:
  #       name: APPNAME
  #   - configMapRef:
  #     name: CONFIG_DATA
  #   schedule: "* * * * *"
  #   externalsecret:
  #     name: APPNAME
  #     dataFrom: tier/ENV/data/APPNAME
  #   command: ["/bin/sh"]
  #   args:
  #     - "-c"
  #     - "echo $(date) - hello from $ECHO_VAR"
  #     - "echo $(date) - loaded secret $SECRET_DATA"
  #     - "echo $(date) - loaded config $CONFIG_DATA"
    # volumes:
    #   - name: APPNAME-volume
    #     secret:
    #       secretName: APPNAME
    #   - name: tmp-volume
    #     emptyDir: {}
    # volumeMounts:
    #   - name: APPNAME-volume
    #     mountPath: "/app/env"
    #   - name: tmp-volume
    #     mountPath: "/tmp"
    # podAnnotations:
    #   container.apparmor.security.beta.kubernetes.io/JOBNAME: runtime/default
    # podSecurityContext:
    #   fsGroup: 1000
    #   seccompProfile:
    #     type: RuntimeDefault
    # securityContext:
    #   privileged: false
    #   allowPrivilegeEscalation: false
    #   readOnlyRootFilesystem: true
    #   runAsNonRoot: true
    #   runAsUser: 1000
    #   runAsGroup: 2000
    #   capabilities:
    #     drop:
    #       - ALL
    # resources:
    #   limits:
    #     cpu: 50m
    #     memory: 256Mi
    #   requests:
    #     cpu: 50m
    #     memory: 256Mi
    # fluentbit:
    #   repository: registry.dbiz.vn/devops/fluentbit
    #   tag: v0.0.5
    #   env: ENV
    #   secretName: fluentbit-v2
    # networkpolicy:
    #   ingress:
    #     - {}
    #   egress:
    #     - {}
  #   failedJobsHistoryLimit: 1
  #   successfulJobsHistoryLimit: 3
  #   concurrencyPolicy: Forbid
  #   restartPolicy: OnFailure

  #   nodeSelector:
  #     type: ssd
  #   tolerations:
  #   - effect: NoSchedule
  #     operator: Exists
  #   affinity:
  #     nodeAffinity:
  #       requiredDuringSchedulingIgnoredDuringExecution:
  #         nodeSelectorTerms:
  #         - matchExpressions:
  #           - key: kubernetes.io/e2e-az-name
  #             operator: In
  #             values:
  #             - e2e-az1
  #             - e2e-az2
