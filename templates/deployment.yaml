{{- $service := .Values.service }}
{{- range $deploy := .Values.deployment }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $deploy.name }}
  labels:
    app.kubernetes.io/name: {{ $deploy.name }}
  {{- with $deploy.labels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if not $deploy.autoscaling.enabled }}
  replicas: {{ $deploy.replicaCount }}
  {{- end }}
  {{- if $deploy.strategy }}
  {{- with $deploy.strategy }}
  strategy:
{{ toYaml . | indent 4 }}
    {{- end }}
  {{- end }}
  selector:
    matchLabels:
      app: {{ $deploy.labels.app }}
      env: {{ $deploy.labels.env }}
  template:
    metadata:
      {{- with $deploy.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        app.kubernetes.io/name: {{ $deploy.name }}
      {{- with $deploy.labels }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with $deploy.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- if $deploy.serviceAccountName }}
      serviceAccountName: {{ $deploy.serviceAccountName }}
      {{- else }}
      serviceAccountName: internal-app
      {{- end }}
      automountServiceAccountToken: false
{{- if $deploy.hostAliases }}
      hostAliases:
{{ toYaml $deploy.hostAliases | indent 8 }}
{{- end }}
      securityContext:
        {{- toYaml $deploy.podSecurityContext | nindent 8 }}
      containers:
      {{- if $deploy.containerName }}
        - name: {{ $deploy.containerName }}
      {{- else }}
        - name: {{ $deploy.name }}
      {{- end }}
          securityContext:
            {{- toYaml $deploy.securityContext | nindent 12 }}
          image: "{{ $deploy.image.repository }}:{{ $deploy.image.tag }}"
          imagePullPolicy: {{ $deploy.image.pullPolicy }}
          {{- with $deploy.env }}
          env:
{{ toYaml . | indent 12 }}
          {{- end }}
          {{- if $deploy.envFrom }}
          envFrom:
          {{- $deploy.envFrom | toYaml | nindent 12 }}
          {{- end }}
          {{- with $deploy.volumeMounts }}
          volumeMounts:
{{ toYaml . | indent 12 }}
            {{- end }}
          {{- if $deploy.command }}
          command: {{ $deploy.command }}
            {{- end }}
          {{- with $deploy.args }}
          args:
{{ toYaml . | indent 12 }}
            {{- end }}
          {{- if $service.enabled }}
          ports:
            - name: http
              containerPort: {{ $service.targetPort }}
              protocol: TCP
          {{- end }}
          {{- with $deploy.livenessProbe }}
          livenessProbe:
{{ toYaml . | indent 12 }}
          {{- end }}
          {{- with $deploy.readinessProbe }}
          readinessProbe:
{{ toYaml . | indent 12 }}
          {{- end }}
          {{- with $deploy.lifecycle }}
          lifecycle:
{{ toYaml . | indent 12 }}
          {{- end }}
          resources:
            {{- toYaml $deploy.resources | nindent 12 }}

        {{- if eq $deploy.fluentbit.env "production" }}
        - name: fluentbit
          image: {{ $deploy.fluentbit.repository }}:{{ $deploy.fluentbit.tag }}
          imagePullPolicy: Always
          envFrom:
          - secretRef:
              {{- if $deploy.fluentbit.secretName }}
              name: {{ $deploy.fluentbit.secretName }}
              {{- else }}
              name: fluentbit
              {{- end }}
          securityContext:
            runAsUser: 0
          env:
            - name: namespace
              value: {{ $deploy.fluentbit.env }}
            - name: appname
            {{- if $deploy.containerName }}
              value: {{ $deploy.containerName }}
            {{- else }}
              value: {{ $deploy.name }}
            {{- end }}
          ports:
            - containerPort: 2020
          readinessProbe:
            httpGet:
              path: /api/v1/metrics/prometheus
              port: 2020
          livenessProbe:
            httpGet:
              path: /
              port: 2020
          resources:
            requests:
              cpu: 5m
              memory: 10Mi
            limits:
              cpu: 1
              memory: 1Gi
          volumeMounts:
            - name: varlog
              mountPath: /var/log
              readOnly: true
            - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              name: internal-app-token
              readOnly: true
        {{- end }}

        {{- if $deploy.fluentbitBigdata }}
        - name: fluentbit-bigdata
          image: {{ $deploy.fluentbitBigdata.repository }}:{{ $deploy.fluentbitBigdata.tag }}
          imagePullPolicy: Always
          envFrom:
          - secretRef:
              name: fluentbit
          securityContext:
            runAsUser: 0
          env:
            - name: namespace
              value: {{ $deploy.fluentbitBigdata.env }}
            - name: appname
              value: {{ $deploy.name }}
          ports:
            - containerPort: 2020
          resources:
            requests:
              cpu: 5m
              memory: 10Mi
            limits:
              cpu: 1
              memory: 1Gi
          volumeMounts:
            - name: varlog
              mountPath: /var/log
              readOnly: true
        {{- end }}

        {{- if $deploy.redis }}
        - name: redis
          image: {{ $deploy.redis.image }}
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh", "-c", "sleep 10; pkill -9 redis-server;"]
          volumeMounts:
            - mountPath: /mnt/socket
              name: redis-data
            - mountPath: /etc/redis.conf
              name: redis-volume
              subPath: redis.conf
          command:
            - redis-server
            - "/etc/redis.conf"
          ports:
            - containerPort: 6379
        {{- end }}

        {{- if $deploy.logstash }}
        - name: logstash
          image: {{ $deploy.logstash.image }}
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - mountPath: /usr/share/logstash/pipeline/logstash.conf
              name: logstash-volume
              subPath: logstash.conf
            - mountPath: /usr/share/logstash/config/logstash.yml
              name: logstash-volume
              subPath: logstash.yml
        {{- end }}

        {{- if $deploy.phpfpmExporter }}
        - name: php-fpm-exporter
          image: {{ $deploy.phpfpmExporter.repository }}:{{ $deploy.phpfpmExporter.tag }}
          imagePullPolicy: IfNotPresent
          volumeMounts:
          - mountPath: /var/run
            name: socket
          env:
          - name: PHP_FPM_FIX_PROCESS_COUNT
            value: "true"
          - name: PHP_FPM_SCRAPE_URI
            value: "unix:///var/run/php.sock;/fpm_status"
          ports:
            - name: exporter
              containerPort: 9253
              protocol: TCP
        {{- end }}

        {{- if $deploy.haproxy }}
        - name: haproxy
          image: {{ $deploy.haproxy.repository }}:{{ $deploy.haproxy.tag }}
          imagePullPolicy: IfNotPresent
          volumeMounts:
          - name: haproxy-volume
            mountPath: /usr/local/etc/haproxy/haproxy.cfg
            subPath: haproxy.cfg
          resources:
            requests:
              cpu: 5m
              memory: 10Mi
            limits:
              cpu: 1
              memory: 1Gi
        {{- end }}
      {{- if $deploy.initContainers }}
      {{- with $deploy.initContainers }}
      initContainers:
{{ toYaml . | indent 8 }}
        {{- end }}
      {{- end }}
      {{- if $deploy.terminationGracePeriodSeconds }}
      terminationGracePeriodSeconds: {{ $deploy.terminationGracePeriodSeconds }}
      {{- else }}
      terminationGracePeriodSeconds: 30
      {{- end }}
      volumes:
      {{- with $deploy.volumes }} 
{{ toYaml . | indent 8 }}
        {{- end }}
      {{- if eq $deploy.fluentbit.env "production" }}
        - name: varlog
          hostPath:
            path: /var/log
        - name: internal-app-token
          secret:
            defaultMode: 420
            secretName: internal-app-token
        {{- end }}
      {{- if $deploy.phpfpmExporter  }}
        - name: socket
          emptyDir: {}
        {{- end }}
      {{- if $deploy.haproxy  }}
        - name: haproxy-volume
          configMap:
              name: {{ $deploy.name }}-config
        {{- end }}
      {{- if $deploy.redis  }}
        - name: redis-volume
          configMap:
              name: {{ $deploy.name }}-config
        - name: redis-data
          emptyDir: {}
        {{- end }}
      {{- if $deploy.redis  }}
        - name: logstash-volume
          configMap:
              name: {{ $deploy.name }}-config
        {{- end }}
      {{- with $deploy.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $deploy.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $deploy.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}