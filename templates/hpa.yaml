{{- range $deploy := .Values.deployment }}
---
{{- if $deploy.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $deploy.name }}
  labels:
    app: {{ $deploy.name }}
    env: {{ $deploy.labels.env }}
    devops: {{ $deploy.labels.devops }}
    owner: {{ $deploy.labels.owner }}
    tier: {{ $deploy.labels.tier }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $deploy.name }}
  minReplicas: {{ $deploy.autoscaling.minReplicas }}
  maxReplicas: {{ $deploy.autoscaling.maxReplicas }}
  metrics:
    {{- if $deploy.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ $deploy.autoscaling.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if $deploy.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ $deploy.autoscaling.targetMemoryUtilizationPercentage }}
    {{- end }}
{{- end }}
{{- end }}