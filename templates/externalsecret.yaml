{{- range $deploy := .Values.deployment }}
---
{{- if  $deploy.externalsecret -}}
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ $deploy.externalsecret.name }}
spec:
  refreshInterval: "15s"
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: {{ $deploy.externalsecret.name }}
  dataFrom:
  - extract:
      key: {{ $deploy.externalsecret.dataFrom }}
{{- end }}
{{- end }}


{{- range $job := .Values.jobs }}
---
{{- if  $job.externalsecret -}}
apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: {{ $job.externalsecret.name }}
spec:
  refreshInterval: "15s"
  secretStoreRef:
    name: vault-backend
    kind: SecretStore
  target:
    name: {{ $job.externalsecret.name }}
  dataFrom:
  - extract:
      key: {{ $job.externalsecret.dataFrom }}
{{- end }}
{{- end }}