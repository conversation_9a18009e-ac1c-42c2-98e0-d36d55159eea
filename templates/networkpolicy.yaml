{{- range $deploy := .Values.deployment }}
---
{{- if  $deploy.networkpolicy -}}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ $deploy.name }}
  namespace: {{ $deploy.labels.env }}
spec:
  podSelector:
    matchLabels:
      app: {{ $deploy.labels.app }}
      env: {{ $deploy.labels.env }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
{{ toYaml $deploy.networkpolicy.ingress | indent 2}}
  egress:
{{ toYaml $deploy.networkpolicy.egress | indent 2}}
  {{- end }}
{{- end }}


{{- range $job := .Values.jobs }}
---
{{- if  $job.networkpolicy -}}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ $job.name }}
  namespace: {{ $job.labels.env }}
spec:
  podSelector:
    matchLabels:
      app: {{ $job.labels.app }}
      env: {{ $job.labels.env }}
  policyTypes:
  - Ingress
  - Egress
  ingress:
{{ toYaml $job.networkpolicy.ingress | indent 2}}
  egress:
{{ toYaml $job.networkpolicy.egress | indent 2}}
  {{- end }}
{{- end }}