{{- if .Values.service.enabled -}}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "chart.fullname" . }}
  labels:
    app: {{ include "chart.fullname" . }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      protocol: TCP
      nodePort: {{ .Values.service.nodePort }}
      name: http
    {{- if .Values.service.extraPort }}
      {{- range .Values.service.extraPort }}
    - port: {{ .port }}
      targetPort: {{ .targetPort }}
      protocol: {{ .protocol }}
      name: {{ .name }}
      {{- end }}
    {{- end }}
{{- if  .Values.service.selector -}}
  {{- with .Values.service.selector }}
  selector:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- else }}
  selector:
    app: {{ include "chart.fullname" . }}
{{- end }}
{{- end }}