{{- if .Values.ingress.enabled -}}
{{- $fullName := include "chart.fullname" . -}}
{{- $svcPort := .Values.service.port -}}
{{- $global := .Values.global -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels: {{- include "chart.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if .Values.ingress.className }}
  ingressClassName: {{ .Values.ingress.className }}
  {{- end }}
  {{- if .Values.ingress.tls }}
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
  {{- range .Values.ingress.hosts }}
  - host: {{ .host | quote }}
    http:
      paths:
      {{- range .paths }}
      - path: {{ .path }}
        {{- if .pathType }}
        pathType: {{ .pathType }}
        {{- else }}
        pathType: Prefix
        {{- end }}
        backend:
          service:
            name: {{ .backend.serviceName | default $global.appname }}
            port:
              number: {{ .backend.servicePort }}
      {{- end }}
  {{- end }}

---
{{- if eq .Values.ingress.className "public" -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}-private
  labels: {{- include "chart.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | replace "public" "nginx" | nindent 4 }}
  {{- end }}
spec:
  ingressClassName: nginx
  {{- if .Values.ingress.tls }}
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
        {{- range .hosts }}
        - {{ . | quote }}
        {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
  {{- range .Values.ingress.hosts }}
  - host: {{ .host | quote }}
    http:
      paths:
      {{- range .paths }}
      - path: {{ .path }}
        {{- if .pathType }}
        pathType: {{ .pathType }}
        {{- else }}
        pathType: Prefix
        {{- end }}
        backend:
          service:
            name: {{ .backend.serviceName | default $global.appname }}
            port:
              number: {{ .backend.servicePort }}
      {{- end }}
  {{- end }}
  {{- end }}
{{- end }}