{{- $release_name := .Release.Name }}
{{- $global := .Values.global }}

{{- range $job := .Values.jobs }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: "{{ $release_name }}"
  labels:
    app.kubernetes.io/name: {{ $job.name | default $global.appname }}
  {{- with $job.labels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  concurrencyPolicy: {{ $job.concurrencyPolicy }}
  failedJobsHistoryLimit: {{ $job.failedJobsHistoryLimit }}
  jobTemplate:
    spec:
      template:
        metadata:
          labels:
            app: "{{ $release_name }}"
            env: {{ $job.labels.env }}
          {{- with $job.podAnnotations }}
          annotations:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        spec:
          serviceAccountName: internal-app
          shareProcessNamespace: true
          securityContext:
            {{- toYaml $job.podSecurityContext | nindent 12 }}
          automountServiceAccountToken: false
          {{- with $job.imagePullSecrets }}
          imagePullSecrets:
{{ toYaml . | indent 12 }}
          {{- end }}
          {{- with $job.hostAliases }}          
          hostAliases:
{{ toYaml . | indent 12 }}
          {{- end }}
          containers:
          - image: "{{ $job.image.repository | default $global.image.repository }}:{{ $job.image.tag | default $global.image.tag }}"
            imagePullPolicy: {{ $job.image.imagePullPolicy | default $global.image.pullPolicy }}
            name: {{ $job.name | default $global.appname }}
            securityContext:
              {{- toYaml $job.securityContext | nindent 14 }}
            {{- with $job.env }}
            env:
{{ toYaml . | indent 12 }}
            {{- end }}
            {{- with $job.envFrom }}
            envFrom:
{{ toYaml . | indent 12 }}
            {{- end }}
            {{- if $job.command }}
            command: {{ $job.command }}
            {{- end }}
            {{- with $job.args }}
            args:
{{ toYaml . | indent 12 }}
              {{- end }}
            {{- with $job.resources }}
            resources:
{{ toYaml . | indent 14 }}
            {{- end }}
            {{- with $job.volumeMounts }}
            volumeMounts:
{{ toYaml . | indent 12 }}
            {{- end }}
          {{- if $job.fluentbit }}
            {{- if eq $job.fluentbit.env "production" }}
          - name: fluentbit
            image: {{ $job.fluentbit.repository }}:{{ $job.fluentbit.tag }}
            imagePullPolicy: Always
            envFrom:
            - secretRef:
                {{- if $job.fluentbit.secretName }}
                name: {{ $job.fluentbit.secretName }}
                {{- else }}
                name: fluentbit
                {{- end }}
            securityContext:
              runAsUser: 0
            env:
              - name: namespace
                value: {{ $job.fluentbit.env }}
              - name: appname
                value: {{ $job.name }}
            ports:
              - containerPort: 2020
            readinessProbe:
              httpGet:
                path: /api/v1/metrics/prometheus
                port: 2020
            livenessProbe:
              httpGet:
                path: /
                port: 2020
            resources:
              requests:
                cpu: 5m
                memory: 10Mi
              limits:
                cpu: 1
                memory: 1Gi
            volumeMounts:
              - name: varlog
                mountPath: /var/log
                readOnly: true
              - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
                name: internal-app-token
                readOnly: true
            lifecycle:
              preStop:
                exec:
                  command: [
                    # Gracefully shutdown
                    "/bin/sleep", "30"
                  ]
          {{- end }}
        {{- end }}
          {{- with $job.nodeSelector }}
          nodeSelector:
{{ toYaml . | indent 12 }}
          {{- end }}
          {{- with $job.affinity }}
          affinity:
{{ toYaml . | indent 12 }}
          {{- end }}
          {{- with $job.tolerations }}
          tolerations:
{{ toYaml . | indent 12 }}
          {{- end }}
          restartPolicy: {{ $job.restartPolicy }}
          {{- with $job.volumes }}
          volumes:
{{ toYaml . | indent 12 }}
          {{- end }}
        {{- if $job.fluentbit }}
          {{- if eq $job.fluentbit.env "production" }}
            - name: varlog
              hostPath:
                path: /var/log
            - name: internal-app-token
              secret:
                defaultMode: 420
                secretName: internal-app-token
          {{- end }}
        {{- end }}       
  schedule: {{ $job.schedule | quote }}
  successfulJobsHistoryLimit: {{ $job.successfulJobsHistoryLimit }}
{{- end }}
