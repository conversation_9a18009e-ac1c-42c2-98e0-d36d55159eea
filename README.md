## Helm v3 installation
```
$ curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3
$ chmod 700 get_helm.sh
$ ./get_helm.sh
```

## Get the chart

```
$ helm registry login reg.ams.net.vn -u <USERNAME> -p <PASSSWORD or TOKEN>
Login Succeeded
$ helm pull oci://reg.ams.net.vn/infra/kubernetes/helm-chart/helm-chart --version 0.1.0
# after helm version 3.8
$ helm registry login -u cicd reg.ams.net.vn
```

## Deployment
```
$ helm registry login reg.ams.net.vn -u <USERNAME> -p <PASSSWORD or TOKEN>
$ helm upgrade --history-max=5 -i ${APP_NAME} oci://reg.ams.net.vn/infra/kubernetes/helm-chart/helm-chart -f values-testing.yaml -n testing
$ kubectl rollout restart deployment.v1.apps/${APP_NAME} -n testing
```

## Validate values
```
$ helm registry login reg.ams.net.vn -u <USERNAME> -p <PASSSWORD or TOKEN>
$ helm template -f values-example.yaml app-test oci://reg.ams.net.vn/infra/kubernetes/helm-chart/helm-chart --output-dir=out -n testing
```


## Contribute to this chart
```
$ helm template -f values-example.yaml test . --output-dir=out
$ helm package .
$ helm registry login reg.ams.net.vn -u <USERNAME> -p <PASSSWORD or TOKEN>
Login Succeeded
$ helm push helm-chart-0.1.0.tgz oci://reg.ams.net.vn/infra/kubernetes/helm-chart
```